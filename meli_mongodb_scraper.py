"""
Script to scrape data from MercadoLibre API and save it to MongoDB.

This script fetches all items from the /users/{user_id}/items/search endpoint
and saves them to a MongoDB database. It then fetches detailed information for
all items using the /items/{item_id} endpoint, filters the active ones, and
saves the details of active items to a separate collection. It also extracts
unique category IDs from all items and fetches their attributes using the
/categories/{category_id}/attributes endpoint, saving them to another collection.
"""
import asyncio
import sys
import os

from dotenv import load_dotenv
from gobots_api import GoBotsAPIClient
from mercadolibre_client import MercadoLibreClient
from mongodb_service import MongoDBService
from logging_config import setup_logging, get_logger

# Setup centralized logging
setup_logging("meli_scraper")
logger = get_logger(__name__)

class MeliMongoDBScraper:
    """Class to scrape MercadoLibre API data and save it to MongoDB."""

    def __init__(self, user_id: int, access_token: str, mongo_uri: str, db_name: str):
        """Initialize with user ID, access token and MongoDB connection details.

        Args:
            user_id: MercadoLibre user ID
            access_token: MercadoLibre API access token
            mongo_uri: MongoDB connection URI
            db_name: MongoDB database name
        """
        self.user_id = user_id
        self.meli_client = MercadoLibreClient(access_token, user_id)
        self.mongodb_service = MongoDBService(mongo_uri, db_name)


    async def scrape_and_save_all_data(self):
        """Main method to scrape all data and save to MongoDB."""
        # Connect to MongoDB
        if not self.mongodb_service.connect():
            return

        try:
            # Fetch and save account metrics using endpoint-based collection naming
            logger.info("Fetching and saving account metrics...")

            # Fetch and save reputation metrics
            logger.info("Fetching reputation metrics from API")
            account_metrics = await self.meli_client.get_account_metrics()
            logger.info("Reputation metrics fetched successfully")
            self.mongodb_service.save_api_response("/users", account_metrics, "id")

            # Fetch all items regardless of status
            logger.info("Fetching all items from user account...")
            all_item_ids = await self.meli_client.get_all_items()
            logger.info(f"Found {len(all_item_ids)} items")

            # Save the item IDs list as well
            self.mongodb_service.save_api_response("/users/items/search", all_item_ids)

            # Fetch details for all items
            logger.info(f"Fetching details for {len(all_item_ids)} items...")
            item_details_result = await self.meli_client.get_item_details(all_item_ids)
            # Ensure we have a list (when passing a list of IDs, the service returns a list)
            all_item_details = item_details_result if isinstance(item_details_result, list) else [item_details_result]

            # Save item details to MongoDB using the new method
            self.mongodb_service.save_bulk_api_response("/items", all_item_details, "id")

            # Fetch and save purchase experience for all items efficiently
            item_ids = [item["id"] for item in all_item_details if "id" in item and "error" not in item]
            if item_ids:
                logger.info(f"Fetching purchase experience for {len(item_ids)} items (batch processing)...")
                purchase_experiences = await self.meli_client.get_batch_purchase_experience(item_ids)

                # Save purchase experiences individually using the new method
                for i, purchase_exp in enumerate(purchase_experiences):
                    if "error" not in purchase_exp:
                        item_id = item_ids[i]
                        self.mongodb_service.save_api_response(
                            f"/reputation/items/{item_id}/purchase_experience/integrators",
                            purchase_exp
                        )
                    else:
                        logger.warning(f"Skipping purchase experience for item {item_ids[i]} due to error: {purchase_exp.get('error', 'Unknown error')}")

            # Extract unique category IDs from all items
            category_ids = set()
            for item in all_item_details:
                if "category_id" in item and "error" not in item:
                    category_ids.add(item["category_id"])

            logger.info(f"Found {len(category_ids)} unique categories")

            # Fetch and save attributes and trends for each category
            logger.info(f"Fetching and saving category attributes and trends for {len(category_ids)} categories...")

            for category_id in category_ids:
                try:
                    # Fetch and save category attributes
                    attributes = await self.meli_client.get_category_attributes(category_id)
                    if attributes:
                        self.mongodb_service.save_api_response(f"/categories/{category_id}/attributes", attributes)

                    # Fetch and save category trending keywords
                    trends = await self.meli_client.get_category_trends(category_id)
                    if trends:
                        self.mongodb_service.save_api_response(f"/trends/MLB/{category_id}", trends)

                except Exception as e:
                    logger.error(f"Error processing category {category_id}: {str(e)}")

            logger.info("Data scraping and saving completed successfully")
        except Exception as e:
            logger.exception(f"Error during scraping: {str(e)}")
        finally:
            # Close connections
            self.mongodb_service.disconnect()
            await self.meli_client.cleanup()

async def main():
    """Main entry point for the script."""
    # Load environment variables
    load_dotenv()

    # Use command line arguments or environment variables
    user_id_str = os.getenv("MELI_USER_ID")
    if not user_id_str:
        logger.error("No user ID provided. Set MELI_USER_ID in .env")
        return 1

    user_id = int(user_id_str)
    mongo_uri = os.getenv("MONGODB_URI", "mongodb://localhost:27017/")
    db_name = "meli_data"

    try:
        # Fetch access token using dependency injection pattern
        logger.info("Fetching access token...")
        gobot_client = GoBotsAPIClient()
        access_token = await gobot_client.get_user_access_token(user_id)
        if not access_token:
            logger.error(f"Could not get access token for user {user_id}")
            return 1
        logger.info("Access token fetched successfully")

        # Initialize scraper with dependency injection
        scraper = MeliMongoDBScraper(
            user_id=user_id,
            access_token=access_token,
            mongo_uri=mongo_uri,
            db_name=db_name
        )

        # Run the scraper
        await scraper.scrape_and_save_all_data()

        print("\n✅ Data scraping completed successfully")

    except Exception as e:
        logger.exception(f"Error: {str(e)}")
        return 1

    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        sys.exit(130)

