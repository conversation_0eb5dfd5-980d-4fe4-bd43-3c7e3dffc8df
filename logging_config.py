"""
Centralized logging configuration for the Mercado Libre Health Agent.

This module provides a unified logging setup that can be imported by all other modules
to ensure consistent logging behavior across the application.
"""
import logging
import sys
from datetime import datetime
from pathlib import Path

import config


def setup_logging(script_name: str = "meli_app") -> logging.Logger:
    """
    Set up centralized logging configuration.
    
    Args:
        script_name: Name of the script/module for log file naming
        
    Returns:
        Logger instance for the calling module
    """
    # Ensure logs directory exists
    Path(config.LOGS_OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
    
    # Create log filename with timestamp
    log_filename = f"{script_name}_{datetime.now().strftime('%Y%m%d')}.log"
    log_filepath = Path(config.LOGS_OUTPUT_DIR) / log_filename
    
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_filepath, encoding='utf-8')
        ],
        force=True  # Override any existing configuration
    )
    
    # Return logger for the calling module
    return logging.getLogger(__name__)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for a specific module.
    
    Args:
        name: Name of the module (typically __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


# Default logger for this module
logger = logging.getLogger(__name__)
