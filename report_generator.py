"""
Functions to generate the weekly health report.
"""
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Tuple
import os
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import json

from pymongo import MongoClient

import config
from logging_config import get_logger

logger = get_logger(__name__)

class ReportGenerator:
    """Generate weekly health reports for Mercado Libre sellers."""

    def __init__(self, mongo_uri: str, db_name: str = "meli_data"):
        """Initialize with MongoDB connection details.

        Args:
            mongo_uri: MongoDB connection URI
            db_name: MongoDB database name (default: "meli_data")
        """
        self.report_date = datetime.now()
        self.report_data = {}

        # MongoDB connection
        self.mongo_uri = mongo_uri
        self.db_name = db_name
        self.mongo_client = None
        self.db = None
        self.items_collection = None
        self.item_details_collection = None
        self.category_attributes_collection = None
        self.trends_collection = None
        # Separate collections for each metric type
        self.reputation_metrics_collection = None
        self.shipping_metrics_collection = None
        self.sales_metrics_collection = None
        self.claims_metrics_collection = None
        self.cancellations_metrics_collection = None
        self.messages_metrics_collection = None

        # Connect to MongoDB
        self.connect_to_mongodb()

    def connect_to_mongodb(self) -> bool:
        """Connect to MongoDB and set up collections.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.mongo_client = MongoClient(self.mongo_uri)
            self.db = self.mongo_client[self.db_name]

            # Use endpoint-based collection names (new structure)
            self.items_collection = self.db["items"]  # From /items endpoint
            self.item_details_collection = self.db["items"]  # Same as items collection
            self.category_attributes_collection = self.db["categories"]  # From /categories endpoint
            self.trends_collection = self.db["trends"]  # From /trends endpoint
            # Separate collections for each metric type (endpoint-based)
            self.reputation_metrics_collection = self.db["users"]  # From /users endpoint
            self.shipping_metrics_collection = self.db["users"]  # From /users/shipping_metrics endpoint
            self.sales_metrics_collection = self.db["users"]  # From /users/sales_metrics endpoint
            self.claims_metrics_collection = self.db["users"]  # From /users/claims endpoint
            self.cancellations_metrics_collection = self.db["users"]  # From /users/cancellations endpoint
            self.messages_metrics_collection = self.db["messages"]  # From /messages/metrics endpoint

            # Create indexes for collections (using timestamp for time-series data)
            try:
                self.items_collection.create_index("response_body.id")
                self.items_collection.create_index("scraped_at")
                self.categories_collection = self.db["categories"]
                self.categories_collection.create_index("response_body.category_id")
                self.trends_collection.create_index("scraped_at")
                self.reputation_metrics_collection.create_index("scraped_at")
                self.messages_collection = self.db["messages"]
                self.messages_collection.create_index("scraped_at")
            except Exception as e:
                logger.warning(f"Error creating indexes: {str(e)}")

            logger.info(f"Connected to MongoDB database: {self.db_name}")
            return True
        except Exception as e:
            logger.error(f"Error connecting to MongoDB: {str(e)}")
            return False

    def close_mongodb_connection(self):
        """Close the MongoDB connection."""
        if self.mongo_client:
            self.mongo_client.close()
            logger.info("MongoDB connection closed")

    def get_trending_keywords(self, category_id=None):
        """Get trending keywords from MongoDB trends collection.

        Args:
            category_id: Optional category ID to filter keywords by

        Returns:
            List of trending keyword documents
        """
        trending_keywords_data = []

        if self.trends_collection is None:
            logger.error("MongoDB connection not established")
            return trending_keywords_data

        try:
            # If category_id is provided, filter by it (new structure with response_body)
            if category_id:
                # Look for trends data that contains the category_id in the response_body
                trends_cursor = self.trends_collection.find({})
                all_trends = list(trends_cursor)

                # Filter trends that match the category_id
                trending_keywords_data = []
                for trend_doc in all_trends:
                    if "response_body" in trend_doc:
                        response_body = trend_doc["response_body"]
                        # The response_body might be a list of trending keywords
                        if isinstance(response_body, list):
                            trending_keywords_data.extend(response_body)
                        elif isinstance(response_body, dict):
                            trending_keywords_data.append(response_body)

                # If no keywords found for this category, get general trending keywords
                if not trending_keywords_data:
                    logger.debug(f"No trending keywords found for category {category_id}, fetching general trends")
                    # Get all trends as fallback
                    for trend_doc in all_trends:
                        if "response_body" in trend_doc:
                            response_body = trend_doc["response_body"]
                            if isinstance(response_body, list):
                                trending_keywords_data.extend(response_body)
                            elif isinstance(response_body, dict):
                                trending_keywords_data.append(response_body)
            else:
                # Get all trending keywords
                trends_cursor = self.trends_collection.find({})
                all_trends = list(trends_cursor)
                trending_keywords_data = []
                for trend_doc in all_trends:
                    if "response_body" in trend_doc:
                        response_body = trend_doc["response_body"]
                        if isinstance(response_body, list):
                            trending_keywords_data.extend(response_body)
                        elif isinstance(response_body, dict):
                            trending_keywords_data.append(response_body)

            logger.info(f"Found {len(trending_keywords_data)} trending keywords in MongoDB")
        except Exception as e:
            logger.error(f"Error fetching trending keywords from MongoDB: {str(e)}")

        return trending_keywords_data

    async def generate_full_report(self) -> Dict[str, Any]:
        """Generate a complete weekly health report."""
        logger.info("Starting report generation")

        try:
            # Gather all data concurrently
            catalog_task = self.generate_catalog_summary()
            metrics_task = self.generate_account_metrics()
            tags_task = self.generate_tags_summary()
            opportunities_task = self.generate_optimization_opportunities()

            # Wait for all tasks to complete
            catalog_summary = await catalog_task
            account_metrics = await metrics_task
            tags_summary = await tags_task
            optimization_opportunities = await opportunities_task

            # Combine all sections into the final report
            report = {
                "report_date": self.report_date.strftime("%Y-%m-%d"),
                "catalog_summary": catalog_summary,
                "account_metrics": account_metrics,
                "tags_summary": tags_summary,
                "optimization_opportunities": optimization_opportunities
            }

            self.report_data = report
            logger.info("Report generation completed")
            return report
        finally:
            # Close MongoDB connection if it was opened
            if self.mongo_client is not None:
                self.close_mongodb_connection()

    async def generate_catalog_summary(self) -> Dict[str, Any]:
        """Generate the catalog summary section of the report."""
        logger.info("Generating catalog summary")

        # Get active listings from MongoDB
        active_listings = []

        if self.item_details_collection is not None:
            try:
                # Get active listings from MongoDB (new structure with response_body)
                cursor = self.item_details_collection.find({"response_body.status": "active"})
                active_listings_docs = list(cursor)
                # Extract the response_body from each document
                active_listings = [doc["response_body"] for doc in active_listings_docs if "response_body" in doc]
                logger.info(f"Found {len(active_listings)} active listings in MongoDB")
            except Exception as e:
                logger.error(f"Error fetching active listings from MongoDB: {str(e)}")
                active_listings = []

        if not active_listings:
            logger.warning("No active listings found in MongoDB")

        # Get inactive listings count from MongoDB
        inactive_count = 0
        if self.item_details_collection is not None:
            try:
                inactive_count = self.item_details_collection.count_documents({"response_body.status": {"$ne": "active"}})
                logger.info(f"Found {inactive_count} inactive listings in MongoDB")
            except Exception as e:
                logger.error(f"Error counting inactive listings from MongoDB: {str(e)}")
                inactive_count = 0

        # For new listings, we need to check the scraped_at date in MongoDB
        new_count = 0
        if self.item_details_collection is not None:
            try:
                # Get items created in the last 7 days
                seven_days_ago = datetime.now() - timedelta(days=7)
                # Convert to ISO format string for comparison with date_created field
                seven_days_ago_str = seven_days_ago.strftime("%Y-%m-%dT%H:%M:%S.%fZ")

                # Query using string comparison since date_created is stored as a string in response_body
                new_count = self.item_details_collection.count_documents({
                    "response_body.date_created": {"$gte": seven_days_ago_str}
                })
                logger.info(f"Found {new_count} new listings in MongoDB (last 7 days)")
            except Exception as e:
                logger.error(f"Error counting new listings from MongoDB: {str(e)}")
                new_count = 0

        # Analyze listings for quality issues
        low_quality_items = await self._identify_low_quality_items(active_listings)

        # Prepare the summary
        summary = {
            "active_count": len(active_listings),
            "inactive_count": inactive_count,
            "new_listings_count": new_count,
            "low_quality_items": low_quality_items[:config.CATALOG_SUMMARY_ITEMS]
        }

        logger.info(f"Catalog summary completed: {len(active_listings)} active, {inactive_count} inactive, {new_count} new listings")
        return summary

    async def _identify_low_quality_items(self, listings: List[Dict]) -> List[Dict]:
        """Identify listings with quality issues by evaluating title, images, and technical sheet."""
        low_quality_items = []
        MAX_LENGTH_LISTINGS = 5
        for item in listings:
            # Get quality scores for title and images
            title_quality = self._evaluate_title_quality(item)
            image_quality = self._evaluate_image_quality(item)

            # Get technical sheet evaluation
            tech_sheet_eval = await self._evaluate_technical_sheet(item)

            # make a grand quality score
            general_quality_score = (title_quality["scores"]["general_title_score"] + image_quality["general_score"] + tech_sheet_eval["completion_score"]) / 3




            low_quality_items.append({
            "id": item["id"],
            "title": item["title"],
            "permalink": item.get("permalink", ""),
            "quality_scores": {
                "general_score": general_quality_score,
                "title": title_quality["scores"],
                "image": image_quality,
                "technical_specs": {
                    "completion_score": tech_sheet_eval["completion_score"],
                    "total_weight": tech_sheet_eval["total_weight"],
                    "filled_weight": tech_sheet_eval["filled_weight"]
                }
            },
            "missing_attributes": tech_sheet_eval["missing_attributes"]
        })

        top_five_worst_quality_items = sorted(low_quality_items, key=lambda x: x["quality_scores"]["general_score"])[:MAX_LENGTH_LISTINGS]
        return top_five_worst_quality_items

    def _evaluate_title_quality(self, item: Dict) -> Dict:
        """
        Calculate title quality scores based on length, keywords, and character usage.
        Direct Python translation of the JavaScript calcTitleScore function.

        Returns:
            Dict with scores, character details, proportions, and word analysis
        """
        import re
        import unicodedata
        import logging

        title = item.get("title", "")
        logging.debug(f"Processing title: {title}")

        # Determine maximum title length
        title_max_length = max(len(title), 60)
        # Calculate score based on length
        length_score = len(title) / title_max_length
        # Count extra blank spaces
        blank_chars = abs(len(title) - title_max_length)
        logging.debug(f"Title length: {len(title)}, Max length: {title_max_length}, Length score: {length_score}")

        # Define connector words and special characters
        connector_words = {
            "de", "da", "do", "e", "por", "para", "com", "a", "ao", "o", "os", "as", "em",
            "um", "uma", "uns", "umas", "no", "na", "nos", "nas", "seu", "sua", "seus",
            "suas", "este", "esta", "estes", "estas"
        }
        special_chars = {
            ".", ",", ":", ";", "/", "\\", "(", ")", "[", "]", "{", "}", "-", "_", "'",
            "\"", "º", "%", "&", "+", "=", "$", "@", "#", "!"
        }

        # Process title: remove accents, convert to lowercase, and split into words
        normalized_title = unicodedata.normalize('NFD', title.lower())
        normalized_title = re.sub(r'[^\w\s]', '', normalized_title)
        normalized_title = normalized_title.replace('ç', 'c')
        words = normalized_title.split()

        # Identify connectors
        connectors_found = [word for word in words if word in connector_words]
        # Count connector characters (including spaces)
        connector_char_count = sum(len(word) + 1 for word in connectors_found)
        logging.debug(f"Connectors found: {connectors_found}")

        # Identify special characters
        special_found = []
        special_char_count = 0

        for i, char in enumerate(title):
            if char in special_chars:
                special_found.append(char)
                special_char_count += 1
                # Count space after special character if present
                if i < len(title) - 1 and title[i + 1] == ' ':
                    special_char_count += 1

        logging.debug(f"Special characters found: {special_found}")

        # Get trending keywords for the category from MongoDB trends collection
        category_id = item.get("category_id")
        trending_keywords_data = self.get_trending_keywords(category_id)
        logging.debug(f"Trending keywords data: {trending_keywords_data}")

        # Process trending keywords
        trending_keywords = set()
        for kw_item in trending_keywords_data:
            if isinstance(kw_item, dict):
                # Extract keyword from the trends collection document
                # The keyword might be in different fields depending on the structure
                # Common fields might be 'keyword', 'term', or 'text'
                keyword = (
                    kw_item.get("keyword", "")
                ).lower()

                # Normalize the keyword (remove accents, special chars)
                normalized_keyword = unicodedata.normalize('NFD', keyword)
                normalized_keyword = re.sub(r'[^\w\s]', '', normalized_keyword)
                normalized_keyword = normalized_keyword.replace('ç', 'c')

                # Add each word from the keyword to the set of trending keywords
                for word in normalized_keyword.split():
                    if word not in connector_words:
                        trending_keywords.add(word)

        # Find trending keywords in title
        trending_keywords_found = [word for word in words if word in trending_keywords]
        trending_char_count = sum(len(word) + 1 for word in trending_keywords_found)
        logging.debug(f"Trending keywords found: {trending_keywords_found}")

        # Calculate boost from trending keywords
        boosted_by_trending = trending_char_count / title_max_length

        # Find regular keywords (not trending, not connectors, not special)
        regular_keywords_found = [
            word for word in words
            if word not in trending_keywords and word not in connector_words and word not in special_chars
        ]
        regular_char_count = sum(len(word) + 1 for word in regular_keywords_found)
        logging.debug(f"Regular keywords found: {regular_keywords_found}")

        # Calculate score impacts
        compromised_by_blank_space = blank_chars / title_max_length
        compromised_by_connectors = connector_char_count / title_max_length
        compromised_by_special = special_char_count / title_max_length

        connector_char_score = abs(compromised_by_connectors - 1)
        special_char_score = abs(compromised_by_special - 1)

        max_keyword_score = title_max_length * 4
        keyword_score = ((trending_char_count * 4) + regular_char_count) / max_keyword_score

        # Calculate boost from regular keywords
        boosted_by_regular = regular_char_count / title_max_length

        # Calculate final general score
        general_score = (
            (length_score * 0.5) +
            (keyword_score * 0.25) +
            (connector_char_score * 0.125) +
            (special_char_score * 0.125)
        )

        logging.debug(f"Calculated scores: {{'length_score': {length_score}, 'keyword_score': {keyword_score}, "
                     f"'connector_char_score': {connector_char_score}, 'special_char_score': {special_char_score}, "
                     f"'general_score': {general_score}}}")

        # Return comprehensive analysis
        return {
            "scores": {
                "length_score": float(length_score),
                "connector_char_score": float(connector_char_score),
                "special_char_score": float(special_char_score),
                "keyword_score": float(keyword_score),
                "general_title_score": float(general_score)
            },
            "char_details": {
                "total_chars": len(title),
                "blank_chars": blank_chars,
                "connector_chars": connector_char_count,
                "special_chars": special_char_count,
                "trending_chars": trending_char_count,
                "regular_chars": regular_char_count
            },
            "proportions": {
                "compromised_by_blank_space": float(compromised_by_blank_space),
                "compromised_by_connectors": float(compromised_by_connectors),
                "compromised_by_special": float(compromised_by_special),
                "boost_by_trending": float(boosted_by_trending),
                "boost_by_regular": float(boosted_by_regular)
            },
            "words": {
                "connectors_found": connectors_found,
                "special_found": special_found,
                "trending_keywords_found": trending_keywords_found,
                "regular_keywords_found": regular_keywords_found
            }
        }

    def _evaluate_image_quality(self, item: Dict) -> Dict[str, float]:
        """
        Calculate image quality scores based on resolution and quantity.
        Direct Python translation of the JavaScript calcImagesScore function.

        Returns:
            Dict with resolution_score, quantity_score, and general_score (all float values 0-1)
        """
        # Check if item is valid and has pictures
        if not item or not isinstance(item.get("pictures", []), list):
            logger.debug("Invalid listing object or missing pictures array.")
            return {"resolution_score": 0, "quantity_score": 0, "general_score": 0}

        pictures = item.get("pictures", [])

        # Calculate resolution score
        total_quality = 0
        for picture in pictures:
            logger.debug(f"Processing picture: {picture}")

            # Extract height and width from max_size string
            try:
                # In JS it's max_size, in our API response it might be size
                size_str = picture.get("max_size", picture.get("size", "0x0"))
                image_height, image_width = map(int, size_str.split("x"))

                quality_measure = max(image_height, image_width)  # Larger side of the image
                ideal_resolution = 1200  # Ideal resolution according to ML
                threshold = 800  # Minimum resolution to avoid penalty

                # Calculate penalty factor for low resolution
                penalty_factor = (quality_measure / threshold) ** 2

                # Calculate image quality
                image_quality = quality_measure / ideal_resolution
                if quality_measure < threshold:
                    image_quality *= penalty_factor  # Apply penalty

                # Ensure value is between 0 and 1
                image_quality = min(image_quality, 1.0)
                logger.debug(f"Calculated image quality: {image_quality}")

                total_quality += image_quality
            except (ValueError, TypeError, ZeroDivisionError):
                logger.debug(f"Error processing image size: {picture.get('max_size', picture.get('size', 'unknown'))}")
                # Don't add any quality for invalid images

        # Calculate average resolution score
        images_resolution_score = total_quality / len(pictures) if pictures else 0
        logger.debug(f"Images resolution score: {images_resolution_score}")

        # Calculate quantity score
        images_qnt_score = 0
        variations = item.get("variations", [])

        if variations and isinstance(variations, list) and len(variations) > 0:
            # For products with variations
            qnt_accumulator = 0
            for variation in variations:
                logger.debug(f"Processing variation: {variation}")
                num_images = len(variation.get("picture_ids", [])) if variation.get("picture_ids") else 0

                # Add points based on number of images per variation
                if 2 <= num_images <= 3:
                    qnt_accumulator += 1
                elif 4 <= num_images <= 5:
                    qnt_accumulator += 2
                elif num_images >= 6:
                    qnt_accumulator += 3

            # Normalize score
            images_qnt_score = qnt_accumulator / (len(variations) * 3) if variations else 0
        else:
            # For single products without variations
            num_images = len(pictures)
            logger.debug(f"Processing single product with images: {num_images}")

            # Define score based on number of images
            if 2 <= num_images <= 3:
                images_qnt_score = 0.33
            elif 4 <= num_images <= 5:
                images_qnt_score = 0.66
            elif num_images >= 6:
                images_qnt_score = 1.0

        logger.debug(f"Images quantity score: {images_qnt_score}")

        # Calculate general score
        general_score = (images_resolution_score + images_qnt_score) / 2
        logger.debug(f"General score calculated: {general_score}")

        # Return normalized scores
        return {
            "resolution_score": float(images_resolution_score),
            "quantity_score": float(images_qnt_score),
            "general_score": float(general_score)
        }

    async def _evaluate_technical_sheet(self, item: Dict) -> Dict:
        """
        Calculate technical specifications score based on attribute completeness.
        Direct Python translation of the JavaScript calcTechnicalSpecsScore function.

        Args:
            item: The listing item to evaluate

        Returns:
            Dict with completion_score, missing_attributes, and not_applied_attributes
        """
        import logging

        # Validate input
        if not item or not item.get("category_id"):
            logging.debug("Invalid listing object or missing category_id.")
            return {
                "completion_score": 0,
                "missing_attributes": [],
                "not_applied_attributes": []
            }

        # Lists of tags and attribute IDs to exclude from calculation
        excluded_tags = ["read_only", "fixed", "inferred", "allow_variations", "variation_attribute", "hidden"]
        excluded_ids = ["SELLER_SKU", "ITEM_CONDITION", "SIZE_GRID_ID", "ENERGY_EFFICIENCY_LABEL", "MANUAL_TITLE"]

        # Fetch category attributes from MongoDB
        category_attributes = []

        if self.category_attributes_collection is not None:
            try:
                # Get attributes from MongoDB (new structure with response_body)
                category_doc = self.category_attributes_collection.find_one({"response_body.category_id": item["category_id"]})
                if category_doc and "response_body" in category_doc:
                    response_body = category_doc["response_body"]
                    if "attributes" in response_body:
                        category_attributes = response_body["attributes"]
                    elif isinstance(response_body, list):
                        # If response_body is directly the attributes list
                        category_attributes = response_body
                    logging.debug(f"Found {len(category_attributes)} attributes for category {item['category_id']} in MongoDB")
            except Exception as e:
                logging.error(f"Error fetching attributes from MongoDB: {str(e)}")

        if not category_attributes:
            logging.warning(f"No attributes found for category {item['category_id']} in MongoDB")
            return {
                "completion_score": 0,
                "missing_attributes": [],
                "not_applied_attributes": []
            }

        if not isinstance(category_attributes, list):
            logging.debug("Invalid category attributes format.")
            return {
                "completion_score": 0,
                "missing_attributes": [],
                "not_applied_attributes": []
            }

        total_weight = 0
        filled_weight = 0
        missing_attributes = []
        not_applied_attributes = []

        for attribute in category_attributes:
            # Skip attributes with excluded tags or IDs
            if attribute.get("id") in excluded_ids:
                continue

            # Check if attribute has any excluded tags
            attribute_tags = attribute.get("tags", {})
            if any(attribute_tags.get(tag) for tag in excluded_tags if attribute_tags.get(tag)):
                continue

            # Determine if attribute is required or hidden
            required = attribute_tags.get("required") or attribute_tags.get("new_required")
            hidden = attribute_tags.get("hidden")

            # Calculate attribute weight
            attribute_relevance = attribute.get("relevance", 0)
            attribute_score = abs(attribute_relevance - 3) + (2 if required else 0) + (1 if hidden else 0)
            total_weight += attribute_score

            # Check if attribute is filled in the listing
            listing_attribute = next((attr for attr in item.get("attributes", []) if attr.get("id") == attribute.get("id")), None)

            if listing_attribute:
                # Check if attribute is marked as not applicable
                value_id = listing_attribute.get("value_id")
                value_name = listing_attribute.get("value_name")

                if (value_id == -1 or value_name == -1 or
                    value_name is None or
                    (isinstance(value_name, str) and value_name.strip() == "")):
                    not_applied_attributes.append({
                        "id": attribute.get("id"),
                        "name": attribute.get("name")
                    })
                    logging.debug(f"Attribute {attribute.get('id')} marked as not applicable.")
                else:
                    filled_weight += attribute_score
            else:
                missing_attributes.append({
                    "id": attribute.get("id"),
                    "name": attribute.get("name")
                })
                logging.debug(f"Attribute {attribute.get('id')} is missing.")

        # Calculate final score
        completion_score = float(filled_weight / total_weight) if total_weight > 0 else 0

        logging.debug(f"Total Weight: {total_weight}")
        logging.debug(f"Filled Weight: {filled_weight}")
        logging.debug(f"Technical Specs Completion Score: {completion_score}")
        logging.debug(f"Missing Attributes: {missing_attributes}")
        logging.debug(f"Not Applied Attributes: {not_applied_attributes}")

        # Generate issues list for backward compatibility
        issues = []

        # Add issues based on completion score
        if completion_score < 0.5:
            issues.append("incomplete_specifications")

        # Add issues for specific missing attributes
        basic_required_attrs = ["BRAND", "MODEL"]
        for attr in missing_attributes:
            attr_id = attr.get("id")
            if attr_id in basic_required_attrs:
                issues.append(f"missing_{attr_id.lower()}")

        # Add issue if too many attributes are missing
        if len(missing_attributes) > 5:
            issues.append("many_missing_attributes")

        # Return comprehensive result
        return {
            "total_weight": float(total_weight),
            "filled_weight": float(filled_weight),
            "completion_score": float(completion_score),
            "missing_attributes": missing_attributes,
            "not_applied_attributes": not_applied_attributes,
            "issues": issues
        }



    async def generate_account_metrics(self) -> Dict[str, Any]:
        """Generate the account metrics section of the report."""
        logger.info("Generating account metrics")

        # Initialize metrics data
        reputation = {}
        shipping = {}
        sales = {}
        claims = []
        cancellations = []
        messages = {}

        # Get metrics from MongoDB using endpoint-based collections (new structure)
        try:
            # Get the latest reputation metrics from users collection
            if self.reputation_metrics_collection is not None:
                reputation_doc = self.reputation_metrics_collection.find_one(
                    {}, sort=[("scraped_at", -1)]
                )
                if reputation_doc and "response_body" in reputation_doc:
                    reputation = reputation_doc["response_body"]

            # Note: For now, we'll use placeholder data for other metrics since they would be in separate collections
            # In a real implementation, you would need to query the appropriate collections based on endpoints

            # Placeholder data for shipping, sales, claims, cancellations
            # These would come from their respective endpoint-based collections
            shipping = {}
            sales = {}
            claims = []
            cancellations = []

            # Get the latest messages metrics from messages collection
            if self.messages_metrics_collection is not None:
                messages_doc = self.messages_metrics_collection.find_one(
                    {}, sort=[("scraped_at", -1)]
                )
                if messages_doc and "response_body" in messages_doc:
                    messages = messages_doc["response_body"]

            logger.info("Retrieved account metrics from MongoDB endpoint-based collections")
        except Exception as e:
            logger.error(f"Error retrieving account metrics from MongoDB: {str(e)}")
            logger.warning("Using default empty values for account metrics")

        # Process conversion rate and identify top factors
        conversion_rate = sales.get("conversion_rate", 0)
        detractors, strengtheners = await self._analyze_conversion_factors(sales)

        # Process shipping metrics
        on_time_rate = shipping.get("on_time_rate", 0)

        # Process claims and cancellations
        claims_count = len(claims) if isinstance(claims, list) else 0
        cancellations_count = len(cancellations) if isinstance(cancellations, list) else 0

        # Process response time
        avg_response_time = messages.get("average_response_time", 0)

        # Process buyer experience
        buyer_experience = reputation.get("transactions", {}).get("ratings", {}).get("positive", 0)

        # Get account reputation and evolution
        current_reputation = reputation.get("level_id", "")
        # The level_progress field might not be available in the response from the /users/{user_id} endpoint
        # We'll use a default value of 0 if it's not available
        reputation_progress = reputation.get("level_progress", 0)

        metrics = {
            "conversion_rate": conversion_rate,
            "top_detractors": detractors,
            "top_strengtheners": strengtheners,
            "on_time_shipping_rate": on_time_rate,
            "claims_count": claims_count,
            "cancellations_count": cancellations_count,
            "avg_response_time": avg_response_time,
            "buyer_experience": buyer_experience,
            "reputation": {
                "current_level": current_reputation,
                "progress": reputation_progress
            }
        }

        logger.info("Account metrics completed")
        return metrics

    async def _analyze_conversion_factors(self, sales_data: Dict) -> Tuple[List[Dict], List[Dict]]:
        """Analyze factors affecting conversion rate."""
        # This would typically involve complex analysis of various factors
        # For this example, we'll create some placeholder data

        # In a real implementation, you would analyze sales_data to determine these factors
        # Example of how we would use the data:
        # Get the conversion rate but we're not using it in this example implementation
        _ = sales_data.get("conversion_rate", 0)
        detractors = [
            {"factor": "Slow shipping", "impact": -0.12},
            {"factor": "High prices", "impact": -0.08},
            {"factor": "Low-quality images", "impact": -0.07},
            {"factor": "Incomplete descriptions", "impact": -0.05},
            {"factor": "Poor reviews", "impact": -0.04}
        ]

        strengtheners = [
            {"factor": "Competitive pricing", "impact": 0.15},
            {"factor": "Fast responses", "impact": 0.10},
            {"factor": "Detailed product information", "impact": 0.08},
            {"factor": "High-quality images", "impact": 0.07},
            {"factor": "Free shipping", "impact": 0.06}
        ]

        return detractors, strengtheners

    async def generate_tags_summary(self) -> Dict[str, Any]:
        """Generate the critical tags section of the report."""
        logger.info("Generating tags summary")

        # Since we don't have tags in MongoDB, we'll use dummy data
        # In a real implementation, you would store these tags in MongoDB

        # Dummy data for demonstration purposes
        tags = [
            {"id": "tag1", "type": "negative", "category": "shipping", "name": "Late delivery", "impact": 0.8},
            {"id": "tag2", "type": "negative", "category": "shipping", "name": "Wrong address", "impact": 0.5},
            {"id": "tag3", "type": "negative", "category": "product", "name": "Defective product", "impact": 0.9},
            {"id": "tag4", "type": "negative", "category": "product", "name": "Wrong product", "impact": 0.7},
            {"id": "tag5", "type": "negative", "category": "customer_service", "name": "Slow response", "impact": 0.6},
            {"id": "tag6", "type": "positive", "category": "shipping", "name": "Fast delivery", "impact": 0.8},
            {"id": "tag7", "type": "positive", "category": "product", "name": "High quality", "impact": 0.9}
        ]

        # Filter for negative tags
        negative_tags = [tag for tag in tags if tag.get("type") == "negative"]

        # Group tags by category
        tags_by_category = {}
        for tag in negative_tags:
            category = tag.get("category", "other")
            if category not in tags_by_category:
                tags_by_category[category] = []
            tags_by_category[category].append(tag)

        # Count tags by category
        tag_counts = {category: len(tags) for category, tags in tags_by_category.items()}

        # Get top tags by impact
        top_tags = sorted(negative_tags, key=lambda x: x.get("impact", 0), reverse=True)[:10]

        summary = {
            "total_negative_tags": len(negative_tags),
            "tags_by_category": tag_counts,
            "top_impact_tags": top_tags
        }

        logger.info(f"Tags summary completed: {len(negative_tags)} negative tags found")
        return summary

    async def generate_optimization_opportunities(self) -> List[Dict]:
        """Generate optimization opportunities based on the data."""
        logger.info("Generating optimization opportunities")

        opportunities = []

        # Get active listings for analysis (limit to 100 to avoid processing too many)
        active_listings = []

        if self.item_details_collection is not None:
            try:
                # Get active listings from MongoDB (limit to 100, new structure with response_body)
                cursor = self.item_details_collection.find({"response_body.status": "active"}).limit(100)
                active_listings_docs = list(cursor)
                # Extract the response_body from each document
                active_listings = [doc["response_body"] for doc in active_listings_docs if "response_body" in doc]
                logger.info(f"Found {len(active_listings)} active listings in MongoDB for optimization analysis")
            except Exception as e:
                logger.error(f"Error fetching active listings from MongoDB: {str(e)}")
                active_listings = []

        if not active_listings:
            logger.warning("No active listings found in MongoDB for optimization analysis")
            return opportunities

        # Check for short titles
        short_titles = [item for item in active_listings if len(item.get("title", "")) < config.TITLE_MIN_LENGTH]
        if short_titles:
            opportunities.append({
                "type": "short_titles",
                "count": len(short_titles),
                "message": f"Você tem {len(short_titles)} anúncios com título abaixo de {config.TITLE_MIN_LENGTH} caracteres. Títulos mais completos têm +12% de conversão.",
                "examples": [{"id": item["id"], "title": item["title"]} for item in short_titles[:3]]
            })

        # Check for missing or low-quality images
        low_quality_images = [
            item for item in active_listings
            if not item.get("pictures", []) or
            any(int(pic.get("size", "0").split("x")[0]) < config.IMAGE_MIN_QUALITY for pic in item.get("pictures", []))
        ]
        if low_quality_images:
            opportunities.append({
                "type": "low_quality_images",
                "count": len(low_quality_images),
                "message": f"Você tem {len(low_quality_images)} anúncios com imagens de baixa qualidade. Imagens de alta resolução aumentam as vendas em até 15%.",
                "examples": [{"id": item["id"], "title": item["title"]} for item in low_quality_images[:3]]
            })

        # Check for items without free shipping
        no_free_shipping = [item for item in active_listings if not item.get("shipping", {}).get("free_shipping", False)]
        if no_free_shipping:
            opportunities.append({
                "type": "no_free_shipping",
                "count": len(no_free_shipping),
                "message": f"Você tem {len(no_free_shipping)} anúncios sem frete grátis. Anúncios com frete grátis têm +20% de conversão.",
                "examples": [{"id": item["id"], "title": item["title"]} for item in no_free_shipping[:3]]
            })

        # Check for items with few or no sales
        # This would require additional API calls in a real implementation

        logger.info(f"Optimization opportunities completed: {len(opportunities)} opportunities found")
        return opportunities

    def save_report_to_file(self, format: str = "json") -> str:
        """Save the report to a file in the specified format."""
        if not self.report_data:
            raise ValueError("No report data available. Generate a report first.")

        # Create output directory if it doesn't exist
        os.makedirs(config.REPORT_OUTPUT_DIR, exist_ok=True)

        # Generate filename with timestamp
        timestamp = self.report_date.strftime("%Y%m%d_%H%M%S")
        filename = f"meli_health_report_{timestamp}"

        if format.lower() == "json":
            filepath = os.path.join(config.REPORT_OUTPUT_DIR, f"{filename}.json")
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(self.report_data, f, ensure_ascii=False, indent=2)

        elif format.lower() == "html":
            # This would require implementing HTML template rendering
            # For simplicity, we'll just create a basic HTML file
            filepath = os.path.join(config.REPORT_OUTPUT_DIR, f"{filename}.html")
            with open(filepath, "w", encoding="utf-8") as f:
                f.write("<html><body><pre>")
                f.write(json.dumps(self.report_data, ensure_ascii=False, indent=2))
                f.write("</pre></body></html>")

        else:
            raise ValueError(f"Unsupported format: {format}")

        logger.info(f"Report saved to {filepath}")
        return filepath
