"""
Main script for generating Mercado Libre weekly health reports.
"""
import asyncio
import argparse
import sys

import config
from report_generator import ReportGenerator
from logging_config import setup_logging, get_logger

# Setup centralized logging
setup_logging("meli_report")
logger = get_logger(__name__)

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Generate Mercado Libre weekly health report")
    parser.add_argument("--format", choices=["json", "html"], default="json", help="Output format for the report")
    parser.add_argument("--mongodb-uri", help="MongoDB URI (overrides .env)")
    parser.add_argument("--db-name", help="MongoDB database name (overrides .env)")
    args = parser.parse_args()

    # Use command line arguments if provided, otherwise use from config
    mongo_uri = args.mongodb_uri or config.MONGODB_URI
    db_name = args.db_name or config.MONGODB_DB_NAME

    if not mongo_uri:
        logger.error("No MongoDB URI provided. Set MONGODB_URI in .env or use --mongodb-uri")
        return 1

    try:
        # Initialize report generator with MongoDB connection
        report_generator = ReportGenerator(mongo_uri=mongo_uri, db_name=db_name)

        # Generate the report
        logger.info("Starting report generation process")
        await report_generator.generate_full_report()

        # Save the report to a file
        output_file = report_generator.save_report_to_file(format=args.format)
        logger.info(f"Report saved to {output_file}")

        print(f"\n✅ Report successfully generated and saved to {output_file}")

    except Exception as e:
        logger.exception(f"Error generating report: {str(e)}")
        return 1
    finally:
        # No cleanup needed
        pass

    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        sys.exit(130)
