"""
Centralized MongoDB service for MercadoLibre data operations.

This service provides a clean interface for all MongoDB operations,
following clean code principles and single responsibility principle.
Data is stored with endpoint-based collection names and complete API responses
with scraped_at timestamp added directly to the data.
"""
from datetime import datetime
from typing import List, Dict, Optional, Union
import re

import pymongo
from pymongo import MongoClient
from pymongo.results import BulkWriteResult, UpdateResult, InsertOneResult
from logging_config import get_logger

logger = get_logger(__name__)


class MongoDBService:
    """Centralized service for all MongoDB operations with endpoint-based collections."""

    def __init__(self, mongo_uri: str, db_name: str):
        """Initialize the MongoDB service.

        Args:
            mongo_uri: MongoDB connection URI
            db_name: Database name
        """
        self.mongo_uri = mongo_uri
        self.db_name = db_name
        self.client: Optional[MongoClient] = None
        self.db = None

    def connect(self) -> bool:
        """Establish connection to MongoDB.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.client = MongoClient(self.mongo_uri)
            self.db = self.client[self.db_name]

            # Clean up old indexes that might conflict with new structure
            self._cleanup_old_indexes()

            # Create new indexes for the new structure
            self._create_new_indexes()

            logger.info(f"Connected to MongoDB database: {self.db_name}")
            return True
        except Exception as e:
            logger.error(f"Error connecting to MongoDB: {str(e)}")
            return False

    def disconnect(self) -> None:
        """Close the MongoDB connection."""
        if self.client:
            self.client.close()
            logger.info("MongoDB connection closed")

    def _endpoint_to_collection_name(self, endpoint: str) -> str:
        """Convert API endpoint to collection name.

        Args:
            endpoint: API endpoint (e.g., '/users/{user_id}', '/items/{item_id}')

        Returns:
            Collection name (e.g., 'users', 'items')
        """
        # Remove leading slash and parameters
        clean_endpoint = endpoint.lstrip('/')
        # Extract the first part of the path
        parts = clean_endpoint.split('/')
        if parts:
            # Remove parameter placeholders like {user_id}, {item_id}
            collection_name = re.sub(r'\{[^}]+\}', '', parts[0]).strip('_')
            return collection_name if collection_name else parts[0]
        return 'unknown'

    def _create_document(self, data: Union[Dict, List]) -> Union[Dict, List]:
        """Create a document with the API response and timestamp.

        Args:
            data: API response data

        Returns:
            Document ready for MongoDB insertion with scraped_at field added directly
        """
        scraped_at = datetime.now()

        if isinstance(data, dict):
            # Make a copy to avoid modifying the original data
            document = data.copy()
            document["scraped_at"] = scraped_at
            return document
        elif isinstance(data, list):
            # For lists, we'll wrap in a document with the list and timestamp
            return {
                "items": data,
                "scraped_at": scraped_at
            }
        else:
            # For other types, wrap in a document
            return {
                "data": data,
                "scraped_at": scraped_at
            }

    def _cleanup_old_indexes(self) -> None:
        """Clean up old indexes that might conflict with the new structure."""
        if self.db is None:
            logger.warning("Database connection not established, skipping index cleanup")
            return

        try:
            # List of old collections that might have conflicting indexes
            old_collections = [
                "meli_item_details", "meli_category_attributes", "trends",
                "account_metrics", "shipping_metrics", "sales_metrics",
                "claims_metrics", "cancellations_metrics", "messages_metrics",
                "purchase_experience"
            ]

            existing_collections = self.db.list_collection_names()
            for collection_name in old_collections:
                if collection_name in existing_collections:
                    collection = self.db[collection_name]
                    try:
                        # Drop all indexes except _id
                        indexes = list(collection.list_indexes())
                        for index in indexes:
                            index_name = index.get("name")
                            if index_name and index_name != "_id_":
                                collection.drop_index(index_name)
                                logger.debug(f"Dropped index {index_name} from {collection_name}")
                    except Exception as e:
                        logger.warning(f"Error cleaning indexes for {collection_name}: {str(e)}")

            logger.info("Old indexes cleaned up successfully")
        except Exception as e:
            logger.warning(f"Error during index cleanup: {str(e)}")

    def _create_new_indexes(self) -> None:
        """Create indexes for the new data structure."""
        if self.db is None:
            logger.warning("Database connection not established, skipping index creation")
            return

        try:
            # Create indexes for common collections
            index_configs = {
                "items": [
                    ("id", 1),
                    ("status", 1),
                    ("scraped_at", -1)
                ],
                "users": [
                    ("id", 1),
                    ("scraped_at", -1)
                ],
                "categories": [
                    ("category_id", 1),
                    ("scraped_at", -1)
                ],
                "trends": [
                    ("scraped_at", -1)
                ],
                "reputation": [
                    ("scraped_at", -1)
                ],
                "messages": [
                    ("scraped_at", -1)
                ]
            }

            for collection_name, indexes in index_configs.items():
                collection = self.db[collection_name]
                for index_spec in indexes:
                    try:
                        if isinstance(index_spec, tuple) and len(index_spec) == 2:
                            collection.create_index([(index_spec[0], index_spec[1])])
                        else:
                            collection.create_index(index_spec)
                        logger.debug(f"Created index {index_spec} on {collection_name}")
                    except Exception as e:
                        logger.debug(f"Index {index_spec} on {collection_name} already exists or error: {str(e)}")

            logger.info("New indexes created successfully")
        except Exception as e:
            logger.warning(f"Error creating new indexes: {str(e)}")

    def _validate_connection(self) -> bool:
        """Validate that MongoDB connection is established.

        Returns:
            True if connected, False otherwise
        """
        if self.client is None or self.db is None:
            logger.error("MongoDB connection not established")
            return False
        return True

    def save_api_response(self, endpoint: str, data: Union[Dict, List],
                         unique_key: Optional[str] = None) -> Optional[Union[InsertOneResult, UpdateResult]]:
        """Save API response data to MongoDB using endpoint-based collection name.

        Args:
            endpoint: API endpoint (used to determine collection name)
            data: API response data
            unique_key: Optional unique key for upsert operations

        Returns:
            MongoDB operation result if successful, None otherwise
        """
        if not self._validate_connection():
            return None

        if not data:
            logger.warning(f"No data to save for endpoint {endpoint}")
            return None

        collection_name = self._endpoint_to_collection_name(endpoint)
        if self.db is None:
            logger.error("Database connection not established")
            return None

        collection = self.db[collection_name]
        document = self._create_document(data)

        try:
            if unique_key and isinstance(data, dict) and unique_key in data:
                # Upsert operation for data with unique keys
                result = collection.update_one(
                    {unique_key: data[unique_key]},
                    {"$set": document},
                    upsert=True
                )
                action = "Saved new" if result.upserted_id else "Updated existing"
                logger.info(f"{action} record in '{collection_name}' collection for {unique_key}: {data[unique_key]}")
                return result
            else:
                # Insert operation for time-series or non-unique data
                result = collection.insert_one(document)
                logger.info(f"Saved record in '{collection_name}' collection with ID: {result.inserted_id}")
                return result
        except Exception as e:
            logger.error(f"Error saving data to '{collection_name}' collection: {str(e)}")
            return None

    def save_bulk_api_response(self, endpoint: str, data_list: List[Dict],
                              unique_key: str) -> Optional[BulkWriteResult]:
        """Save multiple API responses with bulk operations.

        Args:
            endpoint: API endpoint (used to determine collection name)
            data_list: List of API response data
            unique_key: Unique key for upsert operations

        Returns:
            BulkWriteResult if successful, None otherwise
        """
        if not self._validate_connection():
            return None

        if not data_list:
            logger.warning(f"No data to save for endpoint {endpoint}")
            return None

        collection_name = self._endpoint_to_collection_name(endpoint)
        if self.db is None:
            logger.error("Database connection not established")
            return None

        collection = self.db[collection_name]

        # Prepare bulk operations
        operations = []
        for data in data_list:
            if unique_key in data:
                document = self._create_document(data)
                operations.append(
                    pymongo.UpdateOne(
                        {unique_key: data[unique_key]},
                        {"$set": document},
                        upsert=True
                    )
                )

        if not operations:
            logger.warning(f"No valid data to save for endpoint {endpoint}")
            return None

        try:
            result = collection.bulk_write(operations)
            logger.info(f"Saved {result.upserted_count} new records, "
                       f"updated {result.modified_count} existing records in '{collection_name}' collection")
            return result
        except Exception as e:
            logger.error(f"Error saving bulk data to '{collection_name}' collection: {str(e)}")
            return None


