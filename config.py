import os


# API Configuration
MELI_API_BASE_URL = "https://api.mercadolibre.com"
MELI_USER_ID = os.getenv("MELI_USER_ID")
GOBOTS_API_TOKEN = os.getenv("GOBOTS_API_TOKEN")

# MongoDB Configuration
MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
MONGODB_DB_NAME = os.getenv("MONGODB_DB_NAME", "meli_data")

# Report Configuration
REPORT_OUTPUT_DIR = os.getenv("REPORT_OUTPUT_DIR", "./reports")

# Logs Configuration
LOGS_OUTPUT_DIR = os.getenv("LOGS_OUTPUT_DIR", "./logs")

# Thresholds for quality metrics
TITLE_MIN_LENGTH = 40  # Minimum title length for good quality
IMAGE_MIN_QUALITY = 800  # Minimum image resolution (width)
RESPONSE_TIME_THRESHOLD = 12  # Hours for good response time

# Report sections configuration
CATALOG_SUMMARY_ITEMS = 10  # Number of low-quality items to include
TOP_DETRACTORS_COUNT = 5  # Number of top detractors to show
TOP_STRENGTHENERS_COUNT = 5  # Number of top strengtheners to show
