import os
import aiohttp
from typing import Optional, Dict, Any, List


class GoBotsAPIClient:
    """
    A client for interacting with the GoBots API.
    Handles authentication and provides methods to fetch user data.
    """
    
    def __init__(self, token: Optional[str] = None, env_var: str = 'GOBOTS_API_TOKEN'):
        """
        Initialize the GoBots API client.
        
        Args:
            token: Optional API token. If not provided, will try to get from environment variable.
            env_var: Name of the environment variable containing the API token (default: 'GOBOTS_API_TOKEN')
            
        Raises:
            ValueError: If no token is provided and not found in environment variables
        """
        self.base_url = 'https://askhere.gobots.com.br'
        self.token = token or os.getenv(env_var)
        
        if not self.token:
            raise ValueError(
                "API token not provided and not found in environment variables. "
                "Either pass the token directly or set it in the GOBOTS_API_TOKEN environment variable."
            )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """
        Generate the authorization headers with the access token.
        
        Returns:
            dict: Headers with authorization token
        """
        return {'Authorization': f'Bearer {self.token}'}
    
    async def _make_request(self, endpoint: str) -> Optional[Dict[str, Any]]:
        """
        Make a GET request to the GoBots API.
        
        Args:
            endpoint: API endpoint to call (e.g., '/ml/all')
            
        Returns:
            dict: JSON response if successful, None otherwise
        """
        url = f"{self.base_url}{endpoint}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=self._get_auth_headers()) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return None
    
    async def get_all_users(self) -> List[Dict[str, Any]]:
        """
        Fetch all users' data from the GoBots API.
        
        Returns:
            list: List of user data dictionaries
        """
        return await self._make_request('/ml/all') or []
    
    def _extract_user_token(self, user_id: int, users_data: List[Dict[str, Any]]) -> Optional[str]:
        """
        Extract access token for a specific user from the users data.
        
        Args:
            user_id: The user ID to find
            users_data: List of user data dictionaries
            
        Returns:
            str: Access token if found, None otherwise
        """
        for user in users_data:
            if user.get('user_id') == user_id:
                return user.get('access_token')
        return None
    
    async def get_user_access_token(self, user_id: int) -> Optional[str]:
        """
        Get the access token for a specific user by their ID.
        
        Args:
            user_id: The user ID to get the access token for
            
        Returns:
            str: Access token if found, None otherwise
        """
        users_data = await self.get_all_users()
        return self._extract_user_token(user_id, users_data)
