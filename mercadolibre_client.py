"""
Unified MercadoLibre API client with dependency injection.

This module combines the API client and service layers into a single, efficient client
that uses dependency injection for access tokens and provides both low-level API access
and high-level service methods.
"""
import aiohttp
import asyncio
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union

import config
from logging_config import get_logger

logger = get_logger(__name__)


class MercadoLibreClient:
    """Unified MercadoLibre API client with dependency injection and batch processing."""

    def __init__(self, access_token: str, user_id: int, request_delay: float = 0.1):
        """Initialize the client with dependency injection.

        Args:
            access_token: MercadoLibre API access token
            user_id: User ID for API calls
            request_delay: Delay in seconds between requests to prevent rate limiting (default: 0.1s)
        """
        self.access_token = access_token
        self.user_id = str(user_id)
        self.base_url = config.MELI_API_BASE_URL
        self.session = None
        self.request_delay = request_delay

    async def __aenter__(self):
        """Set up the aiohttp session when entering the context manager."""
        if not self.session:
            self.session = aiohttp.ClientSession(
                headers={"Authorization": f"Bearer {self.access_token}"}
            )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Close the aiohttp session when exiting the context manager."""
        if self.session:
            await self.session.close()
            self.session = None

    async def cleanup(self):
        """Clean up the session."""
        if self.session:
            await self.session.close()
            self.session = None

    async def _ensure_session(self):
        """Ensure the session is initialized."""
        if not self.session:
            self.session = aiohttp.ClientSession(
                headers={"Authorization": f"Bearer {self.access_token}"}
            )

    async def _make_single_request(self, method: str, endpoint: str, retry_count: int = 0, **kwargs) -> Any:
        """Make a single API request with error handling and exponential backoff."""
        await self._ensure_session()
        url = f"{self.base_url}{endpoint}"

        try:
            async with getattr(self.session, method.lower())(url, **kwargs) as response:
                if response.status == 429:  # Rate limited
                    if retry_count >= 3:  # Max 3 retries
                        raise Exception("Rate limit exceeded after 3 retries")

                    retry_after = int(response.headers.get("Retry-After", 1))
                    # Add exponential backoff: base delay + exponential component
                    backoff_delay = retry_after + (2 ** retry_count)
                    logger.warning(f"Rate limited. Waiting for {backoff_delay} seconds (retry {retry_count + 1}/3)")
                    await asyncio.sleep(backoff_delay)
                    return await self._make_single_request(method, endpoint, retry_count + 1, **kwargs)

                if response.status == 401:  # Unauthorized
                    error_text = await response.text()
                    logger.error(f"Unauthorized: {error_text}")
                    raise Exception("Unauthorized: Access token is invalid or expired")

                if response.status >= 400:
                    error_text = await response.text()
                    logger.error(f"API error: {response.status} - {error_text}")
                    raise Exception(f"API error: {response.status} - {error_text}")

                # Add delay before returning to prevent rate limiting
                if self.request_delay > 0:
                    await asyncio.sleep(self.request_delay)

                return await response.json()
        except aiohttp.ClientError as e:
            logger.error(f"Request error: {str(e)}")
            raise Exception(f"Request error: {str(e)}") from e

    async def _make_request(self, method: str, endpoint: str, paginate: bool = False,
                           pagination_key: str = "results", page_size: int = 50,
                           limit: Optional[int] = None, **kwargs) -> Any:
        """Make an API request with optional pagination.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint to call
            paginate: Whether to handle pagination automatically
            pagination_key: The key in the response that contains the items to paginate
            page_size: Number of items per page
            limit: Maximum number of items to return (None for all)
            **kwargs: Additional arguments to pass to the request

        Returns:
            API response or list of paginated results
        """
        # If not paginating, make a simple request
        if not paginate:
            return await self._make_single_request(method, endpoint, **kwargs)

        # Handle pagination
        all_results = []
        offset = 0
        total_items = None

        # Fetch all pages of results
        while True:
            # Update pagination parameters in the endpoint
            current_endpoint = endpoint
            if "offset=" in current_endpoint:
                # Replace existing offset and limit parameters
                current_endpoint = re.sub(r'offset=\d+', f'offset={offset}', current_endpoint)
                current_endpoint = re.sub(r'limit=\d+', f'limit={page_size}', current_endpoint)
            else:
                # Add pagination parameters with current offset value
                if "?" in current_endpoint:
                    current_endpoint += f"&offset={offset}&limit={page_size}"
                else:
                    current_endpoint += f"?offset={offset}&limit={page_size}"

            # Make the request for this page
            response = await self._make_single_request(method, current_endpoint, **kwargs)

            # Get paging information
            paging = response.get("paging", {})
            if total_items is None:
                total_items = paging.get("total", 0)
                logger.info(f"Found {total_items} total items")

                # If a limit is specified, adjust total_items
                if limit is not None and limit < total_items:
                    total_items = limit
                    logger.info(f"Limiting results to {limit} items")

            # Add results from this page
            page_results = response.get(pagination_key, [])
            all_results.extend(page_results)

            # Check if we've reached the limit or the end of results
            if limit is not None and len(all_results) >= limit:
                all_results = all_results[:limit]  # Trim to exact limit
                break

            # Check if we've fetched all pages
            if offset + page_size >= paging.get("total", 0) or not page_results:
                break

            # Move to the next page
            offset += page_size
            logger.debug(f"Fetching next page, offset: {offset}")

        return all_results

    # ========================================
    # HIGH-LEVEL SERVICE METHODS
    # ========================================

    # Catalog methods
    async def get_all_items(self) -> List[str]:
        """Fetch all items from the user's account."""
        endpoint = f"/users/{self.user_id}/items/search"
        return await self._make_request(
            "GET",
            endpoint,
            paginate=True,
            pagination_key="results"
        )

    async def get_active_listings(self, limit: Optional[int] = None, count_only: bool = False) -> Union[List[Dict], int]:
        """Get all active listings for the user or just the count."""
        endpoint = f"/users/{self.user_id}/items/search?status=active"

        if count_only:
            response = await self._make_single_request("GET", f"{endpoint}&limit=1")
            total = response.get("paging", {}).get("total", 0)
            logger.info(f"Total active listings: {total}")
            return total

        item_ids = await self._make_request(
            "GET",
            endpoint,
            paginate=True,
            pagination_key="results",
            limit=limit
        )

        logger.info(f"Fetching details for {len(item_ids)} active listings")
        result = await self.get_item_details(item_ids)
        return result if isinstance(result, list) else [result]

    async def get_inactive_listings(self, limit: Optional[int] = None, count_only: bool = False) -> Union[List[Dict], int]:
        """Get all inactive listings for the user or just the count."""
        endpoint = f"/users/{self.user_id}/items/search?status=paused,closed"

        if count_only:
            response = await self._make_single_request("GET", f"{endpoint}&limit=1")
            total = response.get("paging", {}).get("total", 0)
            logger.info(f"Total inactive listings: {total}")
            return total

        item_ids = await self._make_request(
            "GET",
            endpoint,
            paginate=True,
            pagination_key="results",
            limit=limit
        )

        logger.info(f"Fetching details for {len(item_ids)} inactive listings")
        result = await self.get_item_details(item_ids)
        return result if isinstance(result, list) else [result]

    async def get_new_listings(self, days: int = 7, limit: Optional[int] = None, count_only: bool = False) -> Union[List[Dict], int]:
        """Get listings created in the last X days or just the count."""
        since_date = datetime.now() - timedelta(days=days)
        since_str = since_date.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        endpoint = f"/users/{self.user_id}/items/search?date_from={since_str}"

        if count_only:
            response = await self._make_single_request("GET", f"{endpoint}&limit=1")
            total = response.get("paging", {}).get("total", 0)
            logger.info(f"Total new listings in the last {days} days: {total}")
            return total

        item_ids = await self._make_request(
            "GET",
            endpoint,
            paginate=True,
            pagination_key="results",
            limit=limit
        )

        logger.info(f"Fetching details for {len(item_ids)} new listings from the last {days} days")
        result = await self.get_item_details(item_ids)
        return result if isinstance(result, list) else [result]

    async def get_item_details(self, item_id: Union[str, List[str]]) -> Union[Dict, List[Dict]]:
        """Get detailed information about one or multiple items."""
        # Handle single item case
        if isinstance(item_id, str):
            endpoint = f"/items/{item_id}"
            return await self._make_request("GET", endpoint)

        # Handle batch/multiget case
        if not item_id:  # Empty list
            return []

        # Process in batches of 20 (API limitation for multiget endpoint)
        all_results = []
        batch_size = 20

        for i in range(0, len(item_id), batch_size):
            batch = item_id[i:i+batch_size]

            # Construct the multiget endpoint with comma-separated IDs
            ids_param = ",".join(batch)
            endpoint = f"/items?ids={ids_param}"

            # Make the multiget request
            response = await self._make_request("GET", endpoint)

            # Process the multiget response format
            for item_response in response:
                if item_response.get("code") == 200:
                    all_results.append(item_response.get("body", {}))
                else:
                    # Log error but don't fail the entire batch
                    error_msg = item_response.get("body", {}).get("message", "Unknown error")
                    item_id_with_error = item_response.get("body", {}).get("id", "unknown")
                    logger.warning(f"Error fetching item {item_id_with_error}: {error_msg}")
                    # Add a placeholder with error information
                    all_results.append({
                        "id": item_id_with_error,
                        "error": error_msg,
                        "code": item_response.get("code")
                    })

        return all_results

    async def get_item_description(self, item_id: str) -> Dict:
        """Get the description of a specific item."""
        endpoint = f"/items/{item_id}/description"
        return await self._make_request("GET", endpoint)

    async def get_item_visits(self, item_id: str) -> Dict:
        """Get visit statistics for an item."""
        endpoint = f"/visits/items?ids={item_id}"
        return await self._make_request("GET", endpoint)

    # Account metrics methods
    async def get_account_metrics(self) -> Dict:
        """Get overall account metrics from user data."""
        endpoint = f"/users/{self.user_id}"
        return await self._make_request("GET", endpoint)

    async def get_shipping_metrics(self) -> Dict:
        """Get shipping performance metrics."""
        endpoint = f"/users/{self.user_id}/shipping_metrics"
        return await self._make_request("GET", endpoint)

    async def get_sales_metrics(self, period: str = "last_week") -> Dict:
        """Get sales metrics for a specific period."""
        endpoint = f"/users/{self.user_id}/sales_metrics?period={period}"
        return await self._make_request("GET", endpoint)

    async def get_claims(self, status: str = "all") -> List[Dict]:
        """Get claims/complaints for the seller."""
        endpoint = f"/users/{self.user_id}/claims?status={status}"
        return await self._make_request("GET", endpoint)

    async def get_cancellations(self) -> List[Dict]:
        """Get order cancellations."""
        endpoint = f"/users/{self.user_id}/cancellations"
        return await self._make_request("GET", endpoint)

    async def get_messages_metrics(self) -> Dict:
        """Get messaging response metrics."""
        endpoint = f"/messages/metrics/{self.user_id}"
        return await self._make_request("GET", endpoint)

    # Category methods
    async def get_category_attributes(self, category_id: str) -> List[Dict]:
        """Get attributes for a specific category."""
        endpoint = f"/categories/{category_id}/attributes"
        return await self._make_request("GET", endpoint)

    async def get_category_trends(self, category_id: str) -> List[Dict]:
        """Get trending keywords for a specific category."""
        # The trends endpoint uses the site ID (MLB for Brazil) followed by the category ID
        site_id = category_id[:3] if len(category_id) > 3 else "MLB"
        endpoint = f"/trends/{site_id}/{category_id}"
        try:
            return await self._make_request("GET", endpoint)
        except Exception as e:
            logger.warning(f"Error fetching trends for category {category_id}: {str(e)}")
            return []

    # Quality methods
    async def get_item_health(self, item_id: str) -> Dict:
        """Get health metrics for a specific item."""
        endpoint = f"/items/{item_id}/health"
        return await self._make_request("GET", endpoint)

    async def get_account_tags(self) -> List[Dict]:
        """Get all tags associated with the seller account."""
        endpoint = f"/users/{self.user_id}/tags"
        response = await self._make_request("GET", endpoint)
        return response.get("tags", [])

    async def get_item_purchase_experience(self, item_id: str) -> Dict:
        """Get purchase experience data for a specific item."""
        endpoint = f"/reputation/items/{item_id}/purchase_experience/integrators"
        params = {"locale": "pt-BR"}
        return await self._make_request("GET", endpoint, params=params)

    async def get_batch_purchase_experience(self, item_ids: List[str]) -> List[Dict]:
        """Get purchase experience data for multiple items efficiently."""
        if not item_ids:
            return []

        # Process items concurrently for better performance
        import asyncio

        async def fetch_single_experience(item_id: str) -> Dict:
            try:
                return await self.get_item_purchase_experience(item_id)
            except Exception as e:
                logger.error(f"Error fetching purchase experience for item {item_id}: {str(e)}")
                return {"item_id": item_id, "error": str(e)}

        # Use semaphore to limit concurrent requests (avoid overwhelming the API)
        semaphore = asyncio.Semaphore(5)  # Max 5 concurrent requests (reduced for better rate limiting)

        async def fetch_with_semaphore(item_id: str) -> Dict:
            async with semaphore:
                return await fetch_single_experience(item_id)

        # Execute all requests concurrently
        tasks = [fetch_with_semaphore(item_id) for item_id in item_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and return valid results
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Exception for item {item_ids[i]}: {str(result)}")
                valid_results.append({"item_id": item_ids[i], "error": str(result)})
            else:
                valid_results.append(result)

        return valid_results
